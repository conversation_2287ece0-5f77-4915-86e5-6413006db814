<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic <PERSON>c <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Splash Screen -->
    <div class="splash-screen" id="splashScreen">
        <div class="splash-content">
            <h1>Tic Tac <PERSON></h1>
            <div class="splash-menu">
                <button class="menu-btn" id="newGameBtn">New Game</button>
                <button class="menu-btn" id="gameHistoryBtn">Game History</button>
                <button class="menu-btn" id="settingsBtn">Settings</button>
            </div>
        </div>
    </div>

    <!-- Game History Modal -->
    <div class="modal" id="historyModal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Game History</h2>
            <div class="history-list" id="historyList">
                <!-- History items will be added here dynamically -->
            </div>
        </div>
    </div>

    <!-- Game Result Modal -->
    <div class="modal" id="gameResultModal">
        <div class="modal-content result-modal">
            <div class="result-content">
                <div class="result-icon" id="resultIcon">🎉</div>
                <h2 id="resultTitle">Congratulations!</h2>
                <p id="resultMessage">Player X wins!</p>
                <div class="result-stats">
                    <div class="stat-item">
                        <span class="stat-label">Game Duration</span>
                        <span class="stat-value" id="gameDuration">0:45</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Moves</span>
                        <span class="stat-value" id="totalMoves">7</span>
                    </div>
                </div>
                <div class="auto-restart-info">
                    <small>Auto-restart in <span id="countdownTimer">5</span> seconds</small>
                </div>
                <div class="result-actions">
                    <button class="result-btn primary" id="playAgainBtn">Play Again</button>
                    <button class="result-btn secondary" id="backToMenuFromResult">Main Menu</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content settings-modal">
            <span class="close-btn">&times;</span>
            <div class="settings-header">
                <h2>⚙️ Game Settings</h2>
                <p>Customize your gaming experience</p>
            </div>

            <div class="settings-container">
                <div class="settings-section">
                    <div class="section-header">
                        <h3>👥 Player Names</h3>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="playerX">
                                <span class="player-symbol x-symbol">X</span>
                                Player X
                            </label>
                            <input type="text" id="playerX" placeholder="Enter name for Player X" maxlength="15">
                        </div>
                        <div class="form-group">
                            <label for="playerO">
                                <span class="player-symbol o-symbol">O</span>
                                Player O
                            </label>
                            <input type="text" id="playerO" placeholder="Enter name for Player O" maxlength="15">
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3>🎮 Game Mode</h3>
                    </div>
                    <div class="game-mode-options">
                        <div class="mode-option">
                            <input type="radio" id="multiplayer" name="gameMode" value="multiplayer" checked>
                            <label for="multiplayer" class="mode-label">
                                <div class="mode-icon">👥</div>
                                <div class="mode-info">
                                    <span class="mode-title">Multiplayer</span>
                                    <span class="mode-desc">Play with a friend</span>
                                </div>
                            </label>
                        </div>
                        <div class="mode-option">
                            <input type="radio" id="singlePlayer" name="gameMode" value="singlePlayer">
                            <label for="singlePlayer" class="mode-label">
                                <div class="mode-icon">🤖</div>
                                <div class="mode-info">
                                    <span class="mode-title">Single Player</span>
                                    <span class="mode-desc">Play against AI</span>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="form-group ai-difficulty" id="aiDifficultyGroup">
                        <label for="aiDifficulty">🎯 AI Difficulty:</label>
                        <select id="aiDifficulty">
                            <option value="easy">😊 Easy - Random moves</option>
                            <option value="hard">😈 Hard - Strategic AI</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3>🎨 Theme</h3>
                    </div>
                    <div class="theme-options">
                        <div class="theme-option" data-theme="light">
                            <div class="theme-preview light-theme"></div>
                            <span>Light</span>
                        </div>
                        <div class="theme-option" data-theme="dark">
                            <div class="theme-preview dark-theme"></div>
                            <span>Dark</span>
                        </div>
                        <div class="theme-option" data-theme="blue">
                            <div class="theme-preview blue-theme"></div>
                            <span>Ocean</span>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3>🔊 Sound Effects</h3>
                    </div>
                    <div class="sound-setting">
                        <div class="setting-info">
                            <span class="setting-title">Enable Sounds</span>
                            <span class="setting-desc">Play audio feedback during gameplay</span>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="soundToggle" checked>
                            <label for="soundToggle" class="toggle-label">
                                <span class="toggle-handle"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="settings-actions">
                <button class="save-btn" id="saveSettings">💾 Save Settings</button>
                <button class="reset-settings-btn" id="resetSettings">🔄 Reset to Default</button>
            </div>
        </div>
    </div>

    <!-- Main Game Screen -->
    <div class="game-container" id="gameContainer">
        <div class="header">
            <div class="player-info">
                <div class="player-x">
                    <span class="player-symbol">X</span>
                    <span class="player-name" id="playerXName">Player X</span>
                    <span class="player-score" id="playerXScore">0</span>
                </div>
                <div class="vs">vs</div>
                <div class="player-o">
                    <span class="player-symbol">O</span>
                    <span class="player-name" id="playerOName">Player O</span>
                    <span class="player-score" id="playerOScore">0</span>
                </div>
            </div>
            <button class="menu-btn" id="backToMenu">Menu</button>
        </div>

        <div class="status" id="status">Player X's turn</div>
        
        <div class="board" id="board">
            <div class="cell" data-index="0"></div>
            <div class="cell" data-index="1"></div>
            <div class="cell" data-index="2"></div>
            <div class="cell" data-index="3"></div>
            <div class="cell" data-index="4"></div>
            <div class="cell" data-index="5"></div>
            <div class="cell" data-index="6"></div>
            <div class="cell" data-index="7"></div>
            <div class="cell" data-index="8"></div>
        </div>

        <div class="game-controls">
            <button class="reset-btn" id="reset">Reset Game</button>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="clickSound" src="https://assets.mixkit.co/sfx/preview/mixkit-arcade-game-jump-coin-216.mp3"></audio>
    <audio id="winSound" src="https://assets.mixkit.co/sfx/preview/mixkit-winning-chimes-2015.mp3"></audio>
    <audio id="drawSound" src="https://assets.mixkit.co/sfx/preview/mixkit-retro-arcade-lose-2027.mp3"></audio>

    <script src="script.js"></script>
</body>
</html>