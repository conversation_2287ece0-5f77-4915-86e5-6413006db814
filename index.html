<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tic <PERSON>c <PERSON></title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Splash Screen -->
    <div class="splash-screen" id="splashScreen">
        <div class="splash-content">
            <h1>Tic Tac <PERSON>e</h1>
            <div class="splash-menu">
                <button class="menu-btn" id="newGameBtn">New Game</button>
                <button class="menu-btn" id="gameHistoryBtn">Game History</button>
                <button class="menu-btn" id="settingsBtn">Settings</button>
            </div>
        </div>
    </div>

    <!-- Game History Modal -->
    <div class="modal" id="historyModal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Game History</h2>
            <div class="history-list" id="historyList">
                <!-- History items will be added here dynamically -->
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Game Settings</h2>
            
            <div class="settings-section">
                <h3>Player Names</h3>
                <div class="form-group">
                    <label for="playerX">Player X:</label>
                    <input type="text" id="playerX" placeholder="Enter name for X">
                </div>
                <div class="form-group">
                    <label for="playerO">Player O:</label>
                    <input type="text" id="playerO" placeholder="Enter name for O">
                </div>
            </div>

            <div class="settings-section">
                <h3>Game Mode</h3>
                <div class="radio-group">
                    <input type="radio" id="multiplayer" name="gameMode" value="multiplayer" checked>
                    <label for="multiplayer">Multiplayer</label>
                </div>
                <div class="radio-group">
                    <input type="radio" id="singlePlayer" name="gameMode" value="singlePlayer">
                    <label for="singlePlayer">Single Player vs AI</label>
                </div>
                <div class="form-group" id="aiDifficultyGroup">
                    <label for="aiDifficulty">AI Difficulty:</label>
                    <select id="aiDifficulty">
                        <option value="easy">Easy</option>
                        <option value="hard">Hard</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3>Theme</h3>
                <div class="theme-options">
                    <div class="theme-option" data-theme="light">
                        <div class="theme-preview light-theme"></div>
                        <span>Light</span>
                    </div>
                    <div class="theme-option" data-theme="dark">
                        <div class="theme-preview dark-theme"></div>
                        <span>Dark</span>
                    </div>
                    <div class="theme-option" data-theme="blue">
                        <div class="theme-preview blue-theme"></div>
                        <span>Blue</span>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h3>Sound Effects</h3>
                <div class="toggle-switch">
                    <input type="checkbox" id="soundToggle" checked>
                    <label for="soundToggle" class="toggle-label">
                        <span class="toggle-handle"></span>
                    </label>
                </div>
            </div>

            <button class="save-btn" id="saveSettings">Save Settings</button>
        </div>
    </div>

    <!-- Main Game Screen -->
    <div class="game-container" id="gameContainer">
        <div class="header">
            <div class="player-info">
                <div class="player-x">
                    <span class="player-symbol">X</span>
                    <span class="player-name" id="playerXName">Player X</span>
                    <span class="player-score" id="playerXScore">0</span>
                </div>
                <div class="vs">vs</div>
                <div class="player-o">
                    <span class="player-symbol">O</span>
                    <span class="player-name" id="playerOName">Player O</span>
                    <span class="player-score" id="playerOScore">0</span>
                </div>
            </div>
            <button class="menu-btn" id="backToMenu">Menu</button>
        </div>

        <div class="status" id="status">Player X's turn</div>
        
        <div class="board" id="board">
            <div class="cell" data-index="0"></div>
            <div class="cell" data-index="1"></div>
            <div class="cell" data-index="2"></div>
            <div class="cell" data-index="3"></div>
            <div class="cell" data-index="4"></div>
            <div class="cell" data-index="5"></div>
            <div class="cell" data-index="6"></div>
            <div class="cell" data-index="7"></div>
            <div class="cell" data-index="8"></div>
        </div>

        <div class="game-controls">
            <button class="reset-btn" id="reset">Reset Game</button>
        </div>
    </div>

    <!-- Audio Elements -->
    <audio id="clickSound" src="https://assets.mixkit.co/sfx/preview/mixkit-arcade-game-jump-coin-216.mp3"></audio>
    <audio id="winSound" src="https://assets.mixkit.co/sfx/preview/mixkit-winning-chimes-2015.mp3"></audio>
    <audio id="drawSound" src="https://assets.mixkit.co/sfx/preview/mixkit-retro-arcade-lose-2027.mp3"></audio>

    <script src="script.js"></script>
</body>
</html>