document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const splashScreen = document.getElementById('splashScreen');
    const gameContainer = document.getElementById('gameContainer');
    const newGameBtn = document.getElementById('newGameBtn');
    const gameHistoryBtn = document.getElementById('gameHistoryBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    const backToMenu = document.getElementById('backToMenu');
    const historyModal = document.getElementById('historyModal');
    const settingsModal = document.getElementById('settingsModal');
    const gameResultModal = document.getElementById('gameResultModal');
    const closeBtns = document.querySelectorAll('.close-btn');
    const historyList = document.getElementById('historyList');
    const saveSettingsBtn = document.getElementById('saveSettings');
    const resetSettingsBtn = document.getElementById('resetSettings');
    const playAgainBtn = document.getElementById('playAgainBtn');
    const backToMenuFromResult = document.getElementById('backToMenuFromResult');
    const board = document.getElementById('board');
    const cells = document.querySelectorAll('.cell');
    const status = document.getElementById('status');
    const resetButton = document.getElementById('reset');
    const playerXName = document.getElementById('playerXName');
    const playerOName = document.getElementById('playerOName');
    const playerXScore = document.getElementById('playerXScore');
    const playerOScore = document.getElementById('playerOScore');

    // Result Modal Elements
    const resultIcon = document.getElementById('resultIcon');
    const resultTitle = document.getElementById('resultTitle');
    const resultMessage = document.getElementById('resultMessage');
    const gameDuration = document.getElementById('gameDuration');
    const totalMoves = document.getElementById('totalMoves');
    const countdownTimer = document.getElementById('countdownTimer');
    
    // Audio Elements
    const clickSound = document.getElementById('clickSound');
    const winSound = document.getElementById('winSound');
    const drawSound = document.getElementById('drawSound');
    
    // Game State
    let currentPlayer = 'X';
    let gameState = ['', '', '', '', '', '', '', '', ''];
    let gameActive = true;
    let isAgainstAI = false;
    let aiDifficulty = 'easy';
    let soundsEnabled = true;
    let theme = 'light';
    let gameStartTime = null;
    let moveCount = 0;
    let countdownInterval = null;
    
    // Game History
    let gameHistory = JSON.parse(localStorage.getItem('ticTacToeHistory')) || [];
    let scores = JSON.parse(localStorage.getItem('ticTacToeScores')) || { X: 0, O: 0 };
    
    // Player Names
    let playerNames = JSON.parse(localStorage.getItem('ticTacToePlayerNames')) || { X: 'Player X', O: 'Player O' };
    
    // Winning Conditions
    const winningConditions = [
        [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
        [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
        [0, 4, 8], [2, 4, 6]             // diagonals
    ];
    
    // Initialize the game
    function initGame() {
        updatePlayerNames();
        updateScores();
        applyTheme();
        updateAIDifficultyVisibility();

        // Set initial status
        status.textContent = `${playerNames[currentPlayer]}'s turn`;

        // Initialize game timing
        gameStartTime = new Date();
        moveCount = 0;
    }
    
    // Update player names display
    function updatePlayerNames() {
        playerXName.textContent = playerNames.X;
        playerOName.textContent = playerNames.O;
    }
    
    // Update scores display
    function updateScores() {
        playerXScore.textContent = scores.X;
        playerOScore.textContent = scores.O;
    }
    
    // Apply selected theme
    function applyTheme() {
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem('ticTacToeTheme', theme);
    }
    
    // Play sound effect
    function playSound(sound) {
        if (soundsEnabled) {
            sound.currentTime = 0;
            sound.play().catch(e => console.log('Sound playback prevented:', e));
        }
    }
    
    // Handle cell click
    function handleCellClick(e) {
        const clickedCell = e.target;
        const clickedCellIndex = parseInt(clickedCell.getAttribute('data-index'));

        if (gameState[clickedCellIndex] !== '' || !gameActive) {
            return;
        }

        // Play click sound
        playSound(clickSound);

        // Update game state
        gameState[clickedCellIndex] = currentPlayer;
        clickedCell.textContent = currentPlayer;
        clickedCell.classList.add(currentPlayer.toLowerCase());

        // Increment move count
        moveCount++;

        // Check for win or draw
        checkResult();

        // If playing against AI and game is still active, make AI move
        if (isAgainstAI && gameActive && currentPlayer === 'O') {
            setTimeout(makeAIMove, 500);
        }
    }
    
    // Make AI move
    function makeAIMove() {
        let move;
        
        if (aiDifficulty === 'easy') {
            move = getRandomMove();
        } else {
            move = getBestMove();
        }
        
        if (move !== null) {
            const cell = document.querySelector(`.cell[data-index="${move}"]`);
            cell.click();
        }
    }
    
    // Get random move (easy AI)
    function getRandomMove() {
        const emptyCells = gameState
            .map((cell, index) => cell === '' ? index : null)
            .filter(val => val !== null);
        
        if (emptyCells.length === 0) return null;
        
        const randomIndex = Math.floor(Math.random() * emptyCells.length);
        return emptyCells[randomIndex];
    }
    
    // Get best move (hard AI using minimax algorithm)
    function getBestMove() {
        // Check for immediate win
        for (let i = 0; i < gameState.length; i++) {
            if (gameState[i] === '') {
                gameState[i] = 'O';
                if (checkWin('O')) {
                    gameState[i] = '';
                    return i;
                }
                gameState[i] = '';
            }
        }
        
        // Block player's immediate win
        for (let i = 0; i < gameState.length; i++) {
            if (gameState[i] === '') {
                gameState[i] = 'X';
                if (checkWin('X')) {
                    gameState[i] = '';
                    return i;
                }
                gameState[i] = '';
            }
        }
        
        // Try to take center
        if (gameState[4] === '') return 4;
        
        // Try to take corner
        const corners = [0, 2, 6, 8];
        const emptyCorners = corners.filter(index => gameState[index] === '');
        if (emptyCorners.length > 0) {
            return emptyCorners[Math.floor(Math.random() * emptyCorners.length)];
        }
        
        // Take any available edge
        const edges = [1, 3, 5, 7];
        const emptyEdges = edges.filter(index => gameState[index] === '');
        if (emptyEdges.length > 0) {
            return emptyEdges[Math.floor(Math.random() * emptyEdges.length)];
        }
        
        return null;
    }
    
    // Check if a player has won
    function checkWin(player) {
        return winningConditions.some(condition => {
            return condition.every(index => {
                return gameState[index] === player;
            });
        });
    }
    
    // Check game result
    function checkResult() {
        // Check for win
        if (checkWin(currentPlayer)) {
            // Highlight winning cells
            const winningCondition = winningConditions.find(condition =>
                condition.every(index => gameState[index] === currentPlayer)
            );

            if (winningCondition) {
                winningCondition.forEach(index => {
                    document.querySelector(`.cell[data-index="${index}"]`).classList.add('win');
                });
            }

            // Update status and scores
            status.textContent = `${playerNames[currentPlayer]} wins!`;
            scores[currentPlayer] = (scores[currentPlayer] || 0) + 1;
            localStorage.setItem('ticTacToeScores', JSON.stringify(scores));
            updateScores();

            // Play win sound
            playSound(winSound);

            // Add to history
            addToHistory(`${playerNames[currentPlayer]} (${currentPlayer}) won`, 'win');

            gameActive = false;

            // Show result modal
            setTimeout(() => {
                showGameResult('win', `${playerNames[currentPlayer]} wins!`);
            }, 1000);

            return;
        }

        // Check for draw
        if (!gameState.includes('')) {
            status.textContent = "Game ended in a draw!";

            // Play draw sound
            playSound(drawSound);

            // Add to history
            addToHistory('Game ended in a draw', 'draw');

            gameActive = false;

            // Show result modal
            setTimeout(() => {
                showGameResult('draw', 'It\'s a draw!');
            }, 1000);

            return;
        }

        // Switch player
        currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
        status.textContent = `${playerNames[currentPlayer]}'s turn`;
    }
    
    // Add game result to history
    function addToHistory(result, resultType = 'win') {
        const now = new Date();
        const timestamp = now.toLocaleString();
        const duration = gameStartTime ? Math.floor((new Date() - gameStartTime) / 1000) : 0;

        gameHistory.unshift({
            date: timestamp,
            result: result,
            playerX: playerNames.X,
            playerO: playerNames.O,
            mode: isAgainstAI ? 'Single Player' : 'Multiplayer',
            duration: duration,
            moves: moveCount,
            type: resultType
        });

        // Keep only last 20 games
        if (gameHistory.length > 20) {
            gameHistory.pop();
        }

        localStorage.setItem('ticTacToeHistory', JSON.stringify(gameHistory));
        updateHistoryList();
    }
    
    // Update history list in modal
    function updateHistoryList() {
        historyList.innerHTML = '';

        if (gameHistory.length === 0) {
            historyList.innerHTML = '<div class="no-history">No games played yet. Start playing to see your history!</div>';
            return;
        }

        gameHistory.forEach((game, index) => {
            const historyItem = document.createElement('div');
            historyItem.className = `history-item ${game.type || 'win'}`;

            const resultIcon = game.type === 'draw' ? '🤝' : '🏆';
            const duration = game.duration ? `${Math.floor(game.duration / 60)}:${(game.duration % 60).toString().padStart(2, '0')}` : 'N/A';
            const moves = game.moves || 'N/A';

            historyItem.innerHTML = `
                <div class="history-main">
                    <div class="history-result">
                        <span class="result-icon">${resultIcon}</span>
                        <strong>${game.result}</strong>
                    </div>
                    <div class="history-details">
                        <span class="history-players">${game.playerX} vs ${game.playerO}</span>
                        <span class="history-mode">${game.mode}</span>
                    </div>
                </div>
                <div class="history-stats">
                    <div class="stat">
                        <span class="stat-label">Duration</span>
                        <span class="stat-value">${duration}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Moves</span>
                        <span class="stat-value">${moves}</span>
                    </div>
                    <div class="history-date">${game.date}</div>
                </div>
            `;
            historyList.appendChild(historyItem);
        });
    }
    
    // Show game result modal
    function showGameResult(type, message) {
        const duration = gameStartTime ? Math.floor((new Date() - gameStartTime) / 1000) : 0;
        const durationText = `${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')}`;

        // Update result modal content
        if (type === 'win') {
            resultIcon.textContent = '🎉';
            resultIcon.className = 'result-icon win';
            resultTitle.textContent = 'Congratulations!';
        } else if (type === 'draw') {
            resultIcon.textContent = '🤝';
            resultIcon.className = 'result-icon draw';
            resultTitle.textContent = 'It\'s a Draw!';
        }

        resultMessage.textContent = message;
        gameDuration.textContent = durationText;
        totalMoves.textContent = moveCount.toString();

        // Show modal
        showModal(gameResultModal);

        // Start countdown
        let countdown = 5;
        countdownTimer.textContent = countdown;

        // Clear any existing countdown
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }

        countdownInterval = setInterval(() => {
            countdown--;
            countdownTimer.textContent = countdown;

            if (countdown <= 0) {
                clearInterval(countdownInterval);
                if (gameResultModal.style.display === 'flex') {
                    hideModal(gameResultModal);
                    resetGame();
                }
            }
        }, 1000);
    }

    // Reset game
    function resetGame() {
        currentPlayer = 'X';
        gameState = ['', '', '', '', '', '', '', '', ''];
        gameActive = true;
        status.textContent = `${playerNames[currentPlayer]}'s turn`;
        gameStartTime = new Date();
        moveCount = 0;

        cells.forEach(cell => {
            cell.textContent = '';
            cell.className = 'cell';
            cell.setAttribute('data-index', cell.getAttribute('data-index'));
        });
    }
    
    // Start new game
    function startNewGame() {
        splashScreen.style.display = 'none';
        gameContainer.style.display = 'block';
        resetGame();
    }
    
    // Show modal
    function showModal(modal) {
        modal.style.display = 'flex';
    }
    
    // Hide modal
    function hideModal(modal) {
        modal.style.display = 'none';
    }
    
    // Update AI difficulty visibility
    function updateAIDifficultyVisibility() {
        const aiDifficultyGroup = document.getElementById('aiDifficultyGroup');
        const singlePlayerRadio = document.getElementById('singlePlayer');

        if (singlePlayerRadio && aiDifficultyGroup) {
            if (singlePlayerRadio.checked) {
                aiDifficultyGroup.classList.add('active');
            } else {
                aiDifficultyGroup.classList.remove('active');
            }
        }
    }

    // Save settings
    function saveSettings() {
        // Save player names
        playerNames.X = document.getElementById('playerX').value.trim() || 'Player X';
        playerNames.O = document.getElementById('playerO').value.trim() || 'Player O';
        localStorage.setItem('ticTacToePlayerNames', JSON.stringify(playerNames));

        // Save game mode
        isAgainstAI = document.getElementById('singlePlayer').checked;
        localStorage.setItem('ticTacToeGameMode', isAgainstAI);

        // Save AI difficulty
        aiDifficulty = document.getElementById('aiDifficulty').value;
        localStorage.setItem('ticTacToeAIDifficulty', aiDifficulty);

        // Save theme
        theme = document.querySelector('.theme-option.active')?.dataset.theme || 'light';
        localStorage.setItem('ticTacToeTheme', theme);

        // Save sound settings
        soundsEnabled = document.getElementById('soundToggle').checked;
        localStorage.setItem('ticTacToeSounds', soundsEnabled);

        // Update UI
        updatePlayerNames();
        applyTheme();
        updateAIDifficultyVisibility();

        // Close modal
        hideModal(settingsModal);
    }

    // Reset settings to default
    function resetSettings() {
        // Reset to defaults
        document.getElementById('playerX').value = 'Player X';
        document.getElementById('playerO').value = 'Player O';
        document.getElementById('multiplayer').checked = true;
        document.getElementById('singlePlayer').checked = false;
        document.getElementById('aiDifficulty').value = 'easy';
        document.getElementById('soundToggle').checked = true;

        // Reset theme
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.dataset.theme === 'light') {
                option.classList.add('active');
            }
        });

        updateAIDifficultyVisibility();
    }
    
    // Load settings
    function loadSettings() {
        // Load saved settings from localStorage
        const savedGameMode = localStorage.getItem('ticTacToeGameMode');
        const savedAIDifficulty = localStorage.getItem('ticTacToeAIDifficulty');
        const savedTheme = localStorage.getItem('ticTacToeTheme');
        const savedSounds = localStorage.getItem('ticTacToeSounds');

        if (savedGameMode !== null) isAgainstAI = savedGameMode === 'true';
        if (savedAIDifficulty) aiDifficulty = savedAIDifficulty;
        if (savedTheme) theme = savedTheme;
        if (savedSounds !== null) soundsEnabled = savedSounds === 'true';

        // Load player names
        document.getElementById('playerX').value = playerNames.X;
        document.getElementById('playerO').value = playerNames.O;

        // Load game mode
        document.getElementById(isAgainstAI ? 'singlePlayer' : 'multiplayer').checked = true;

        // Load AI difficulty
        document.getElementById('aiDifficulty').value = aiDifficulty;

        // Load theme
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.dataset.theme === theme) {
                option.classList.add('active');
            }
        });

        // Load sound settings
        document.getElementById('soundToggle').checked = soundsEnabled;

        // Update AI difficulty visibility
        updateAIDifficultyVisibility();
    }
    
    // Event Listeners
    newGameBtn.addEventListener('click', startNewGame);
    gameHistoryBtn.addEventListener('click', () => {
        updateHistoryList();
        showModal(historyModal);
    });
    settingsBtn.addEventListener('click', () => {
        loadSettings();
        showModal(settingsModal);
    });
    backToMenu.addEventListener('click', () => {
        gameContainer.style.display = 'none';
        splashScreen.style.display = 'flex';
    });
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            hideModal(this.closest('.modal'));
        });
    });
    saveSettingsBtn.addEventListener('click', saveSettings);
    resetSettingsBtn.addEventListener('click', resetSettings);
    playAgainBtn.addEventListener('click', () => {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        hideModal(gameResultModal);
        resetGame();
    });
    backToMenuFromResult.addEventListener('click', () => {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        hideModal(gameResultModal);
        gameContainer.style.display = 'none';
        splashScreen.style.display = 'flex';
    });
    cells.forEach(cell => cell.addEventListener('click', handleCellClick));
    resetButton.addEventListener('click', resetGame);

    // Game mode change listeners
    document.getElementById('multiplayer').addEventListener('change', updateAIDifficultyVisibility);
    document.getElementById('singlePlayer').addEventListener('change', updateAIDifficultyVisibility);
    
    // Theme selection
    document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === historyModal) hideModal(historyModal);
        if (e.target === settingsModal) hideModal(settingsModal);
        if (e.target === gameResultModal) hideModal(gameResultModal);
    });
    
    // Initialize
    const savedTheme = localStorage.getItem('ticTacToeTheme');
    if (savedTheme) theme = savedTheme;
    
    const savedSounds = localStorage.getItem('ticTacToeSounds');
    if (savedSounds !== null) soundsEnabled = savedSounds === 'true';
    
    initGame();
});