:root {
    --primary-color: #4a6fa5;
    --secondary-color: #166088;
    --background-color: #f8f9fa;
    --text-color: #333;
    --cell-color: #fff;
    --cell-border: #333;
    --x-color: #ff4757;
    --o-color: #2ed573;
    --status-color: #444;
    --button-bg: #333;
    --button-hover: #555;
    --modal-bg: rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] {
    --primary-color: #6c757d;
    --secondary-color: #495057;
    --background-color: #212529;
    --text-color: #f8f9fa;
    --cell-color: #343a40;
    --cell-border: #6c757d;
    --status-color: #dee2e6;
    --button-bg: #6c757d;
    --button-hover: #5a6268;
}

[data-theme="blue"] {
    --primary-color: #166088;
    --secondary-color: #4a6fa5;
    --background-color: #dbe9ee;
    --text-color: #0a2463;
    --cell-color: #fff;
    --cell-border: #166088;
    --status-color: #0a2463;
    --button-bg: #166088;
    --button-hover: #4a6fa5;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    transition: background-color 0.3s, color 0.3s;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
}

/* Splash Screen */
.splash-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.5s ease-in-out;
}

.splash-content {
    text-align: center;
    color: white;
}

.splash-content h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.splash-menu {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.menu-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1.2rem;
    background-color: white;
    color: var(--primary-color);
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.menu-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Game Container */
.game-container {
    display: none;
    text-align: center;
    background: var(--background-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
    animation: slideUp 0.5s ease-in-out;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.player-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.player-x, .player-o {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 0.2);
}

.player-x .player-symbol {
    color: var(--x-color);
    font-weight: bold;
}

.player-o .player-symbol {
    color: var(--o-color);
    font-weight: bold;
}

.player-name {
    font-weight: 600;
}

.player-score {
    font-weight: bold;
    color: var(--secondary-color);
}

.vs {
    font-weight: bold;
    color: var(--secondary-color);
}

.status {
    margin: 1rem 0;
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--status-color);
}

.board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 10px;
    margin: 1.5rem auto;
    aspect-ratio: 1/1;
    max-width: 400px;
}

.cell {
    background-color: var(--cell-color);
    border: 2px solid var(--cell-border);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s;
}

.cell:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cell.x {
    color: var(--x-color);
}

.cell.o {
    color: var(--o-color);
}

.cell.win {
    animation: pulse 1s infinite;
    background-color: rgba(255, 255, 0, 0.2);
}

.game-controls {
    margin-top: 1.5rem;
}

.reset-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    background-color: var(--button-bg);
    color: white;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
}

.reset-btn:hover {
    background-color: var(--button-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Modals */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-bg);
    z-index: 1001;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease-in-out;
}

.modal-content {
    background-color: var(--background-color);
    padding: 2rem;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
}

.history-list {
    margin-top: 1rem;
}

.history-item {
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
}

.settings-section {
    margin-bottom: 1.5rem;
}

.settings-section h3 {
    margin-bottom: 0.8rem;
    color: var(--secondary-color);
}

.form-group {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.form-group label {
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: var(--cell-color);
    color: var(--text-color);
}

.radio-group {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.radio-group input {
    margin-right: 0.5rem;
}

.theme-options {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.theme-option {
    cursor: pointer;
    text-align: center;
}

.theme-preview {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    margin-bottom: 0.3rem;
    border: 2px solid transparent;
}

.theme-option:hover .theme-preview {
    transform: scale(1.05);
}

.theme-option.active .theme-preview {
    border: 2px solid var(--secondary-color);
}

.light-theme {
    background: linear-gradient(135deg, #f8f9fa 50%, #e9ecef 50%);
}

.dark-theme {
    background: linear-gradient(135deg, #212529 50%, #343a40 50%);
}

.blue-theme {
    background: linear-gradient(135deg, #dbe9ee 50%, #4a6fa5 50%);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-label:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-label {
    background-color: var(--secondary-color);
}

input:checked + .toggle-label:before {
    transform: translateX(30px);
}

.save-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
    margin-top: 1rem;
    width: 100%;
}

.save-btn:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Responsive */
@media (max-width: 500px) {
    .game-container {
        padding: 1rem;
    }
    
    .board {
        max-width: 300px;
    }
    
    .cell {
        font-size: 2.5rem;
    }
    
    .player-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .vs {
        display: none;
    }
}