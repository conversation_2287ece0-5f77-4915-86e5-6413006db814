document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const splashScreen = document.getElementById('splashScreen');
    const gameContainer = document.getElementById('gameContainer');
    const newGameBtn = document.getElementById('newGameBtn');
    const gameHistoryBtn = document.getElementById('gameHistoryBtn');
    const settingsBtn = document.getElementById('settingsBtn');
    const backToMenu = document.getElementById('backToMenu');
    const historyModal = document.getElementById('historyModal');
    const settingsModal = document.getElementById('settingsModal');
    const closeBtns = document.querySelectorAll('.close-btn');
    const historyList = document.getElementById('historyList');
    const saveSettingsBtn = document.getElementById('saveSettings');
    const board = document.getElementById('board');
    const cells = document.querySelectorAll('.cell');
    const status = document.getElementById('status');
    const resetButton = document.getElementById('reset');
    const playerXName = document.getElementById('playerXName');
    const playerOName = document.getElementById('playerOName');
    const playerXScore = document.getElementById('playerXScore');
    const playerOScore = document.getElementById('playerOScore');
    
    // Audio Elements
    const clickSound = document.getElementById('clickSound');
    const winSound = document.getElementById('winSound');
    const drawSound = document.getElementById('drawSound');
    
    // Game State
    let currentPlayer = 'X';
    let gameState = ['', '', '', '', '', '', '', '', ''];
    let gameActive = true;
    let isAgainstAI = false;
    let aiDifficulty = 'easy';
    let soundsEnabled = true;
    let theme = 'light';
    
    // Game History
    let gameHistory = JSON.parse(localStorage.getItem('ticTacToeHistory')) || [];
    let scores = JSON.parse(localStorage.getItem('ticTacToeScores')) || { X: 0, O: 0 };
    
    // Player Names
    let playerNames = JSON.parse(localStorage.getItem('ticTacToePlayerNames')) || { X: 'Player X', O: 'Player O' };
    
    // Winning Conditions
    const winningConditions = [
        [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
        [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
        [0, 4, 8], [2, 4, 6]             // diagonals
    ];
    
    // Initialize the game
    function initGame() {
        updatePlayerNames();
        updateScores();
        applyTheme();
        
        // Set initial status
        status.textContent = `${playerNames[currentPlayer]}'s turn`;
    }
    
    // Update player names display
    function updatePlayerNames() {
        playerXName.textContent = playerNames.X;
        playerOName.textContent = playerNames.O;
    }
    
    // Update scores display
    function updateScores() {
        playerXScore.textContent = scores.X;
        playerOScore.textContent = scores.O;
    }
    
    // Apply selected theme
    function applyTheme() {
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem('ticTacToeTheme', theme);
    }
    
    // Play sound effect
    function playSound(sound) {
        if (soundsEnabled) {
            sound.currentTime = 0;
            sound.play().catch(e => console.log('Sound playback prevented:', e));
        }
    }
    
    // Handle cell click
    function handleCellClick(e) {
        const clickedCell = e.target;
        const clickedCellIndex = parseInt(clickedCell.getAttribute('data-index'));
        
        if (gameState[clickedCellIndex] !== '' || !gameActive) {
            return;
        }
        
        // Play click sound
        playSound(clickSound);
        
        // Update game state
        gameState[clickedCellIndex] = currentPlayer;
        clickedCell.textContent = currentPlayer;
        clickedCell.classList.add(currentPlayer.toLowerCase());
        
        // Check for win or draw
        checkResult();
        
        // If playing against AI and game is still active, make AI move
        if (isAgainstAI && gameActive && currentPlayer === 'O') {
            setTimeout(makeAIMove, 500);
        }
    }
    
    // Make AI move
    function makeAIMove() {
        let move;
        
        if (aiDifficulty === 'easy') {
            move = getRandomMove();
        } else {
            move = getBestMove();
        }
        
        if (move !== null) {
            const cell = document.querySelector(`.cell[data-index="${move}"]`);
            cell.click();
        }
    }
    
    // Get random move (easy AI)
    function getRandomMove() {
        const emptyCells = gameState
            .map((cell, index) => cell === '' ? index : null)
            .filter(val => val !== null);
        
        if (emptyCells.length === 0) return null;
        
        const randomIndex = Math.floor(Math.random() * emptyCells.length);
        return emptyCells[randomIndex];
    }
    
    // Get best move (hard AI using minimax algorithm)
    function getBestMove() {
        // Check for immediate win
        for (let i = 0; i < gameState.length; i++) {
            if (gameState[i] === '') {
                gameState[i] = 'O';
                if (checkWin('O')) {
                    gameState[i] = '';
                    return i;
                }
                gameState[i] = '';
            }
        }
        
        // Block player's immediate win
        for (let i = 0; i < gameState.length; i++) {
            if (gameState[i] === '') {
                gameState[i] = 'X';
                if (checkWin('X')) {
                    gameState[i] = '';
                    return i;
                }
                gameState[i] = '';
            }
        }
        
        // Try to take center
        if (gameState[4] === '') return 4;
        
        // Try to take corner
        const corners = [0, 2, 6, 8];
        const emptyCorners = corners.filter(index => gameState[index] === '');
        if (emptyCorners.length > 0) {
            return emptyCorners[Math.floor(Math.random() * emptyCorners.length)];
        }
        
        // Take any available edge
        const edges = [1, 3, 5, 7];
        const emptyEdges = edges.filter(index => gameState[index] === '');
        if (emptyEdges.length > 0) {
            return emptyEdges[Math.floor(Math.random() * emptyEdges.length)];
        }
        
        return null;
    }
    
    // Check if a player has won
    function checkWin(player) {
        return winningConditions.some(condition => {
            return condition.every(index => {
                return gameState[index] === player;
            });
        });
    }
    
    // Check game result
    function checkResult() {
        // Check for win
        if (checkWin(currentPlayer)) {
            // Highlight winning cells
            const winningCondition = winningConditions.find(condition => 
                condition.every(index => gameState[index] === currentPlayer)
            );
            
            if (winningCondition) {
                winningCondition.forEach(index => {
                    document.querySelector(`.cell[data-index="${index}"]`).classList.add('win');
                });
            }
            
            // Update status and scores
            status.textContent = `${playerNames[currentPlayer]} wins!`;
            scores[currentPlayer] = (scores[currentPlayer] || 0) + 1;
            localStorage.setItem('ticTacToeScores', JSON.stringify(scores));
            updateScores();
            
            // Play win sound
            playSound(winSound);
            
            // Add to history
            addToHistory(`${playerNames[currentPlayer]} (${currentPlayer}) won`);
            
            gameActive = false;
            return;
        }
        
        // Check for draw
        if (!gameState.includes('')) {
            status.textContent = "Game ended in a draw!";
            
            // Play draw sound
            playSound(drawSound);
            
            // Add to history
            addToHistory('Game ended in a draw');
            
            gameActive = false;
            return;
        }
        
        // Switch player
        currentPlayer = currentPlayer === 'X' ? 'O' : 'X';
        status.textContent = `${playerNames[currentPlayer]}'s turn`;
    }
    
    // Add game result to history
    function addToHistory(result) {
        const now = new Date();
        const timestamp = now.toLocaleString();
        
        gameHistory.unshift({
            date: timestamp,
            result: result,
            playerX: playerNames.X,
            playerO: playerNames.O,
            mode: isAgainstAI ? 'Single Player' : 'Multiplayer'
        });
        
        // Keep only last 10 games
        if (gameHistory.length > 10) {
            gameHistory.pop();
        }
        
        localStorage.setItem('ticTacToeHistory', JSON.stringify(gameHistory));
        updateHistoryList();
    }
    
    // Update history list in modal
    function updateHistoryList() {
        historyList.innerHTML = '';
        
        gameHistory.forEach(game => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div>
                    <strong>${game.result}</strong><br>
                    <small>${game.date}</small>
                </div>
                <div>
                    ${game.playerX} vs ${game.playerO}<br>
                    <small>${game.mode}</small>
                </div>
            `;
            historyList.appendChild(historyItem);
        });
    }
    
    // Reset game
    function resetGame() {
        currentPlayer = 'X';
        gameState = ['', '', '', '', '', '', '', '', ''];
        gameActive = true;
        status.textContent = `${playerNames[currentPlayer]}'s turn`;
        
        cells.forEach(cell => {
            cell.textContent = '';
            cell.className = 'cell';
            cell.setAttribute('data-index', cell.getAttribute('data-index'));
        });
    }
    
    // Start new game
    function startNewGame() {
        splashScreen.style.display = 'none';
        gameContainer.style.display = 'block';
        resetGame();
    }
    
    // Show modal
    function showModal(modal) {
        modal.style.display = 'flex';
    }
    
    // Hide modal
    function hideModal(modal) {
        modal.style.display = 'none';
    }
    
    // Save settings
    function saveSettings() {
        // Save player names
        playerNames.X = document.getElementById('playerX').value || 'Player X';
        playerNames.O = document.getElementById('playerO').value || 'Player O';
        localStorage.setItem('ticTacToePlayerNames', JSON.stringify(playerNames));
        
        // Save game mode
        isAgainstAI = document.getElementById('singlePlayer').checked;
        
        // Save AI difficulty
        aiDifficulty = document.getElementById('aiDifficulty').value;
        
        // Save theme
        theme = document.querySelector('.theme-option.active')?.dataset.theme || 'light';
        
        // Save sound settings
        soundsEnabled = document.getElementById('soundToggle').checked;
        
        // Update UI
        updatePlayerNames();
        applyTheme();
        
        // Close modal
        hideModal(settingsModal);
    }
    
    // Load settings
    function loadSettings() {
        // Load player names
        document.getElementById('playerX').value = playerNames.X;
        document.getElementById('playerO').value = playerNames.O;
        
        // Load game mode
        document.getElementById(isAgainstAI ? 'singlePlayer' : 'multiplayer').checked = true;
        
        // Load AI difficulty
        document.getElementById('aiDifficulty').value = aiDifficulty;
        
        // Load theme
        document.querySelectorAll('.theme-option').forEach(option => {
            option.classList.remove('active');
            if (option.dataset.theme === theme) {
                option.classList.add('active');
            }
        });
        
        // Load sound settings
        document.getElementById('soundToggle').checked = soundsEnabled;
    }
    
    // Event Listeners
    newGameBtn.addEventListener('click', startNewGame);
    gameHistoryBtn.addEventListener('click', () => {
        updateHistoryList();
        showModal(historyModal);
    });
    settingsBtn.addEventListener('click', () => {
        loadSettings();
        showModal(settingsModal);
    });
    backToMenu.addEventListener('click', () => {
        gameContainer.style.display = 'none';
        splashScreen.style.display = 'flex';
    });
    closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            hideModal(this.closest('.modal'));
        });
    });
    saveSettingsBtn.addEventListener('click', saveSettings);
    cells.forEach(cell => cell.addEventListener('click', handleCellClick));
    resetButton.addEventListener('click', resetGame);
    
    // Theme selection
    document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === historyModal) hideModal(historyModal);
        if (e.target === settingsModal) hideModal(settingsModal);
    });
    
    // Initialize
    const savedTheme = localStorage.getItem('ticTacToeTheme');
    if (savedTheme) theme = savedTheme;
    
    const savedSounds = localStorage.getItem('ticTacToeSounds');
    if (savedSounds !== null) soundsEnabled = savedSounds === 'true';
    
    initGame();
});